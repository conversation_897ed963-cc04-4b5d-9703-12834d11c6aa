#!/usr/bin/env python3
"""
直接测试云端API端点
"""

import requests
import json
import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_cloud_api_endpoints():
    """直接测试云端API端点"""
    print("=== 直接测试云端API端点 ===")
    
    # 配置
    base_url = "http://8.138.188.26:80/api"
    custom_id = "SM_006"
    
    # 从认证文件读取token
    auth_file = "auth_info.json"
    token = None
    
    try:
        if os.path.exists(auth_file):
            with open(auth_file, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)
                token = auth_data.get('access_token')
                print(f"从认证文件读取到token: {token[:20] if token else 'None'}...")
        else:
            print("认证文件不存在")
            return False
    except Exception as e:
        print(f"读取认证文件失败: {e}")
        return False
    
    if not token:
        print("没有找到有效的token")
        return False
    
    # 准备请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'X-User-ID': custom_id,
        'Content-Type': 'application/json'
    }
    
    print(f"请求头: {headers}")
    
    # 测试端点列表
    endpoints = [
        {
            "name": "移动端评估量表",
            "url": f"{base_url}/mobile/assessments",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "移动端问卷",
            "url": f"{base_url}/mobile/questionnaires", 
            "params": {"custom_id": custom_id}
        },
        {
            "name": "标准评估量表",
            "url": f"{base_url}/assessments",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "标准问卷",
            "url": f"{base_url}/questionnaires",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "用户评估量表",
            "url": f"{base_url}/user-assessments",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "分发评估量表",
            "url": f"{base_url}/distributed-assessments",
            "params": {"custom_id": custom_id}
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        print(f"\n--- 测试 {endpoint['name']} ---")
        print(f"URL: {endpoint['url']}")
        print(f"参数: {endpoint['params']}")
        
        try:
            response = requests.get(
                endpoint['url'],
                headers=headers,
                params=endpoint['params'],
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应类型: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"响应键: {list(data.keys())}")
                        
                        if 'status' in data:
                            print(f"状态: {data['status']}")
                        
                        if 'data' in data:
                            data_content = data['data']
                            print(f"data类型: {type(data_content)}")
                            
                            if isinstance(data_content, list):
                                print(f"data长度: {len(data_content)}")
                                if data_content:
                                    print(f"第一个元素: {data_content[0]}")
                                    if isinstance(data_content[0], dict):
                                        print(f"第一个元素键: {list(data_content[0].keys())}")
                            elif isinstance(data_content, dict):
                                print(f"data字典键: {list(data_content.keys())}")
                                if 'assessments' in data_content:
                                    assessments = data_content['assessments']
                                    print(f"assessments长度: {len(assessments)}")
                                    if assessments:
                                        print(f"第一个评估量表: {assessments[0]}")
                                if 'questionnaires' in data_content:
                                    questionnaires = data_content['questionnaires']
                                    print(f"questionnaires长度: {len(questionnaires)}")
                                    if questionnaires:
                                        print(f"第一个问卷: {questionnaires[0]}")
                            else:
                                print(f"data内容: {data_content}")
                        
                        print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"响应内容: {data}")
                    
                    results.append({
                        "endpoint": endpoint['name'],
                        "status": "success",
                        "data": data
                    })
                    
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
                    results.append({
                        "endpoint": endpoint['name'],
                        "status": "json_error",
                        "error": str(e),
                        "raw_response": response.text
                    })
            else:
                print(f"请求失败: {response.status_code}")
                print(f"错误响应: {response.text}")
                results.append({
                    "endpoint": endpoint['name'],
                    "status": "http_error",
                    "status_code": response.status_code,
                    "error": response.text
                })
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            results.append({
                "endpoint": endpoint['name'],
                "status": "request_error",
                "error": str(e)
            })
    
    # 总结结果
    print("\n=== 测试结果总结 ===")
    for result in results:
        print(f"{result['endpoint']}: {result['status']}")
        if result['status'] == 'success':
            data = result['data']
            if isinstance(data, dict) and 'data' in data:
                data_content = data['data']
                if isinstance(data_content, list):
                    print(f"  -> 返回 {len(data_content)} 个项目")
                elif isinstance(data_content, dict):
                    if 'assessments' in data_content:
                        print(f"  -> 返回 {len(data_content['assessments'])} 个评估量表")
                    if 'questionnaires' in data_content:
                        print(f"  -> 返回 {len(data_content['questionnaires'])} 个问卷")
    
    # 寻找有数据的端点
    successful_endpoints = [r for r in results if r['status'] == 'success']
    if successful_endpoints:
        print(f"\n找到 {len(successful_endpoints)} 个成功的端点")
        
        # 检查哪个端点有实际数据
        for result in successful_endpoints:
            data = result['data']
            if isinstance(data, dict) and 'data' in data:
                data_content = data['data']
                has_data = False
                
                if isinstance(data_content, list) and data_content:
                    has_data = True
                elif isinstance(data_content, dict):
                    if (data_content.get('assessments') and len(data_content['assessments']) > 0) or \
                       (data_content.get('questionnaires') and len(data_content['questionnaires']) > 0):
                        has_data = True
                
                if has_data:
                    print(f"\n🎉 发现有数据的端点: {result['endpoint']}")
                    print(f"建议移动端使用此端点获取数据")
                    return True
    
    print("\n❌ 所有端点都没有返回数据")
    print("建议检查:")
    print("1. 云端是否真的为用户 SM_006 分发了量表/问卷")
    print("2. 分发记录的状态是否正确")
    print("3. API实现是否正确")
    
    return False

def main():
    """主函数"""
    print("开始直接测试云端API端点...\n")
    
    success = test_cloud_api_endpoints()
    
    if success:
        print("\n✅ 找到了有数据的API端点")
    else:
        print("\n❌ 没有找到有数据的API端点")
        print("\n请检查云端分发记录:")
        print("SELECT * FROM assessment_distributions WHERE user_custom_id = 'SM_006';")
        print("SELECT * FROM questionnaire_distributions WHERE user_custom_id = 'SM_006';")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
