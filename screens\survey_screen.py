# 问卷/量表填写与历史答卷界面
from kivy.uix.screenmanager import Screen
from kivy.properties import ListProperty, ObjectProperty, StringProperty
from kivy.clock import Clock
from kivymd.uix.button import MDButton
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import <PERSON><PERSON><PERSON>, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from utils.survey_manager import get_survey_manager
from utils.user_manager import get_user_manager
from kivy.lang import Builder
from kivy.uix.scrollview import ScrollView
from functools import partial
import threading
import json
import logging
from datetime import datetime
from kivymd.app import MDApp as App
from kivy.metrics import dp

# 导入API客户端
from api.api_client import APIClient

# 导入认证管理器
from utils.auth_manager import get_auth_manager, AuthManager

# 导入选项卡组件
from kivymd.uix.tab import MDTabsItem

# 导入Logo组件
from widgets.logo import HealthLogo

# 导入主题
from theme import AppTheme, AppMetrics

# 设置日志
logger = logging.getLogger(__name__)

Builder.load_string('''
<SurveyScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    BoxLayout:
        orientation: 'vertical'
        padding: dp(10)
        spacing: dp(10)

        # 顶部标题和返回按钮
        BoxLayout:
            size_hint_y: None
            height: dp(56)
            spacing: dp(10)

            MDIconButton:
                icon: "arrow-left"
                on_release: root.go_back()
                pos_hint: {"center_y": .5}
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_COLOR

            MDLabel:
                text: '评估量表与问卷'
                halign: 'center'
                valign: 'middle'
                font_size: '20sp'
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_PRIMARY
                bold: True

        # Logo区域
        MDBoxLayout:
            id: logo_container_wrap
            orientation: 'vertical'
            size_hint_y: None
            height: dp(160)
            padding: [0, dp(0), 0, dp(0)]

            # Logo主容器
            MDBoxLayout:
                id: logo_container
                orientation: 'vertical'
                size_hint_y: None
                height: dp(150)

                # 使用统一的HealthLogo组件
                HealthLogo:
                    id: health_logo

        # 内容区域
        BoxLayout:
            orientation: 'vertical'
            padding: dp(10)
            spacing: dp(10)
            md_bg_color: app.theme.CARD_BACKGROUND
            radius: [dp(12)]
            elevation: 2

            # 选项卡标题
            BoxLayout:
                size_hint_y: None
                height: dp(48)
                spacing: dp(10)

                MDButton:
                    text: "评估量表"
                    on_release: root.switch_tab("assessments")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK if root.current_tab == "assessments" else app.theme.TEXT_PRIMARY

                MDButton:
                    text: "问卷"
                    on_release: root.switch_tab("questionnaires")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK if root.current_tab == "questionnaires" else app.theme.TEXT_PRIMARY

                MDButton:
                    text: "历史记录"
                    on_release: root.switch_tab("history")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK if root.current_tab == "history" else app.theme.TEXT_PRIMARY

            # 内容区域
            BoxLayout:
                id: content_area
                orientation: 'vertical'

                # 评估量表内容
                BoxLayout:
                    id: assessments_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 1

                    # 刷新按钮
                    MDButton:
                        text: "刷新量表列表"
                        on_release: root.load_assessments()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 量表列表
                    ScrollView:
                        MDList:
                            id: assessment_list

                # 问卷内容
                BoxLayout:
                    id: questionnaires_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 0
                    size_hint: 1, 0
                    height: 0

                    # 刷新按钮
                    MDButton:
                        text: "刷新问卷列表"
                        on_release: root.load_questionnaires()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 问卷列表
                    ScrollView:
                        MDList:
                            id: questionnaire_list

                # 历史记录内容
                BoxLayout:
                    id: history_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 0
                    size_hint: 1, 0
                    height: 0

                    # 刷新按钮
                    MDButton:
                        text: "刷新历史记录"
                        on_release: root.load_response_history()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 历史记录列表
                    ScrollView:
                        MDList:
                            id: response_history_list
''')

class SurveyScreen(Screen):
    questionnaire_list = ListProperty([])
    assessment_list = ListProperty([])
    current_user = ObjectProperty(None)
    selected_questionnaire = ObjectProperty(None)
    selected_assessment = ObjectProperty(None)
    response_history = ListProperty([])
    api_client = None
    current_tab = StringProperty("assessments")

    def __init__(self, **kwargs):
        super(SurveyScreen, self).__init__(**kwargs)
        # 初始化API客户端
        self.api_client = APIClient()
        # 初始化认证管理器
        self.auth_manager = get_auth_manager()

    def on_pre_enter(self, *args):
        """进入页面前的准备工作"""
        logger.info("进入评估量表与问卷页面")

        # 获取当前用户
        user_manager = get_user_manager()
        self.current_user = user_manager.get_current_user()

        # 设置当前用户到认证管理器，但不检查登录状态
        self.auth_manager.set_current_user(self.current_user)

        # 确保API客户端已初始化
        if not self.api_client:
            self.api_client = APIClient()
            logger.info("初始化API客户端")

        # 使用认证管理器配置API客户端
        self.auth_manager.setup_api_client(self.api_client)

        # 强制刷新用户信息
        self.auth_manager.refresh_user_info()

        # 检查认证状态
        if not self.auth_manager.is_user_authenticated():
            logger.warning("用户未认证，尝试重新加载认证信息")
            if not self.auth_manager.reload_user_auth():
                logger.error("无法重新加载用户认证信息")
                self.show_error("请先登录后再访问此页面")
                self.manager.current = 'login_screen'
                return

        # 加载评估量表
        self.load_assessments()

        # 加载问卷列表
        self.load_questionnaires()

        # 加载历史记录
        self.load_response_history()

    def go_back(self):
        """返回上一个屏幕"""
        self.manager.current = 'homepage_screen'

    def switch_tab(self, tab_name):
        """切换标签页

        Args:
            tab_name: 标签页名称，可以是"assessments", "questionnaires", "history"
        """
        # 更新当前标签
        self.current_tab = tab_name

        # 获取内容区域的子组件
        assessments_content = self.ids.assessments_content
        questionnaires_content = self.ids.questionnaires_content
        history_content = self.ids.history_content

        # 首先隐藏所有内容
        for content in [assessments_content, questionnaires_content, history_content]:
            content.opacity = 0
            content.size_hint = (1, 0)
            content.height = 0

        # 显示选中的内容
        if tab_name == "assessments":
            assessments_content.opacity = 1
            assessments_content.size_hint = (1, 1)
            assessments_content.height = dp(400)  # 使用固定高度而不是None
            # 加载评估量表
            self.load_assessments()
        elif tab_name == "questionnaires":
            questionnaires_content.opacity = 1
            questionnaires_content.size_hint = (1, 1)
            questionnaires_content.height = dp(400)  # 使用固定高度而不是None
            # 加载问卷
            self.load_questionnaires()
        elif tab_name == "history":
            history_content.opacity = 1
            history_content.size_hint = (1, 1)
            history_content.height = dp(400)  # 使用固定高度而不是None
            # 加载历史记录
            self.load_response_history()

    def load_assessments(self):
        """从后端加载评估量表"""
        try:
            # 显示加载对话框
            self.show_loading_dialog("正在加载评估量表...")

            # 在后台线程中加载数据
            threading.Thread(target=self._fetch_assessments_in_background, daemon=True).start()
        except Exception as e:
            logger.error(f"加载评估量表时出错: {str(e)}")
            self.dismiss_loading_dialog()
            self.show_error(f"加载评估量表失败: {str(e)}")

    def _fetch_assessments_in_background(self):
        """在后台线程中从API获取评估量表"""
        try:
            # 获取用户信息
            user_info = self.auth_manager.get_current_user_info()
            custom_id = user_info.get('custom_id')

            if not custom_id:
                logger.warning("无法获取用户custom_id")
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                Clock.schedule_once(lambda dt: self.show_error("用户信息不完整，请重新登录"), 0)
                return

            # 优先从云端获取最新数据，如果失败再使用本地缓存
            if self.api_client and hasattr(self.api_client, 'cloud_api') and self.api_client.cloud_api:
                # 构建请求参数
                params = {'custom_id': custom_id}

                # 调用新的移动端API
                try:
                    logger.info(f"使用移动端API获取评估量表，custom_id: {custom_id}")

                    # 使用新的移动端API方法
                    if hasattr(self.api_client.cloud_api, 'get_mobile_assessments'):
                        result = self.api_client.cloud_api.get_mobile_assessments(custom_id)
                    else:
                        # 如果没有新方法，回退到旧的API调用
                        logger.warning("云端API不支持get_mobile_assessments方法，使用旧的API")

                        # 强制刷新认证管理器中的用户信息
                        self.auth_manager.refresh_user_info()

                        # 使用认证管理器获取请求头
                        headers = self.auth_manager.get_auth_headers()

                        # 确保请求头中包含必要的认证信息
                        if not headers.get('Authorization'):
                            # 尝试从cloud_api获取token
                            if hasattr(self.api_client.cloud_api, 'token') and self.api_client.cloud_api.token:
                                headers['Authorization'] = f"Bearer {self.api_client.cloud_api.token}"

                        # 确保X-User-ID存在
                        if not headers.get('X-User-ID') and custom_id:
                            headers['X-User-ID'] = custom_id

                        # 添加Content-Type头
                        headers['Content-Type'] = 'application/json'

                        logger.info(f"发送API请求，认证头: {headers.get('Authorization', '无')[:15] if headers.get('Authorization') else '无'}..., X-User-ID: {headers.get('X-User-ID', '无')}")

                        # 尝试使用make_request方法
                        if hasattr(self.api_client.cloud_api, 'make_request'):
                            result = self.api_client.cloud_api.make_request(
                                method="GET",
                                endpoint="mobile/assessments",
                                params=params,
                                headers=headers
                            )
                        # 如果没有make_request方法，尝试使用get方法
                        elif hasattr(self.api_client.cloud_api, 'get'):
                            logger.info(f"使用get方法获取评估量表，custom_id: {custom_id}")
                            result = self.api_client.cloud_api.get("mobile/assessments", params=params, headers=headers)
                        else:
                            # 如果都没有，使用requests库直接请求
                            import requests
                            base_url = getattr(self.api_client.cloud_api, 'base_url', 'http://localhost:8006/api')
                        logger.info(f"使用requests库获取评估量表，URL: {base_url}/assessments, custom_id: {custom_id}")

                        # 确保URL格式正确
                        if base_url.endswith('/api'):
                            url = f"{base_url}/assessments"
                        else:
                            url = f"{base_url}/api/assessments"

                        # 发送请求，强制禁用所有代理
                        # 检查requests版本是否支持trust_env参数
                        request_kwargs = {
                            'url': url,
                            'params': params,
                            'headers': headers,
                            'timeout': 10,
                            'proxies': {
                                'http': None,
                                'https': None,
                                'ftp': None,
                                'socks4': None,
                                'socks5': None
                            }
                        }

                        # 尝试添加trust_env参数，如果不支持则忽略
                        try:
                            import inspect
                            sig = inspect.signature(requests.get)
                            if 'trust_env' in sig.parameters:
                                request_kwargs['trust_env'] = False
                        except Exception:
                            # 如果检查失败，不添加trust_env参数
                            pass

                        response = requests.get(**request_kwargs)

                        # 检查响应状态码
                        if response.status_code == 200:
                            # 检查响应内容是否为空
                            if response.text.strip():
                                try:
                                    raw_result = response.json()
                                    logger.info(f"服务器返回数据类型: {type(raw_result)}")

                                    # 检查数据格式，判断是否为列表
                                    if isinstance(raw_result, list):
                                        # 如果是列表，直接作为数据
                                        result = {
                                            "status": "success",
                                            "data": raw_result
                                        }
                                        logger.info(f"服务器返回列表格式数据，包含 {len(raw_result)} 个项目")
                                    elif isinstance(raw_result, dict):
                                        # 如果是字典，检查是否已经是标准格式
                                        if 'status' in raw_result or 'data' in raw_result:
                                            result = raw_result
                                        else:
                                            # 如果是普通字典，包装为标准格式
                                            result = {
                                                "status": "success",
                                                "data": raw_result
                                            }
                                    else:
                                        # 其他情况，包装为标准格式
                                        result = {
                                            "status": "success",
                                            "data": raw_result
                                        }
                                except ValueError:
                                    logger.error(f"响应不是有效的JSON: {response.text[:100]}")
                                    result = {"status": "error", "message": "服务器返回的数据格式不正确"}
                            else:
                                logger.error("服务器返回空响应")
                                result = {"status": "error", "message": "服务器返回空响应"}
                        else:
                            logger.error(f"服务器返回错误状态码: {response.status_code}")

                            # 如果是服务器错误，尝试使用备用服务器
                            if response.status_code >= 500:
                                logger.info("主服务器错误，尝试使用备用服务器")

                                try:
                                    # 从配置中获取备用URL
                                    from utils.app_config import API_CONFIG
                                    backup_url = API_CONFIG.get('BACKUP_URL', 'http://localhost:8006/api')

                                    # 构建备用URL（不包含assessment_id，因为是获取列表）
                                    backup_api_url = f"{backup_url}/assessments"

                                    # 强制刷新认证管理器获取最新token
                                    self.auth_manager.refresh_user_info()
                                    backup_headers = self.auth_manager.get_auth_headers()

                                    # 确保包含X-User-ID
                                    if 'X-User-ID' not in backup_headers and custom_id:
                                        backup_headers['X-User-ID'] = custom_id

                                    # 添加其他可能的认证头
                                    backup_headers['Content-Type'] = 'application/json'

                                    logger.info(f"尝试使用备用服务器获取评估量表列表: {backup_api_url}，带认证头: {backup_headers.get('Authorization', '无')[:15] if backup_headers.get('Authorization') else '无'}..., X-User-ID: {backup_headers.get('X-User-ID', '无')}")
                                    backup_response = requests.get(
                                        backup_api_url,
                                        params=params,
                                        headers=backup_headers,
                                        timeout=10,
                                        proxies={
                                            'http': None,
                                            'https': None
                                        }
                                    )

                                    # 如果第一个路径失败，尝试其他可能的API路径
                                    if backup_response.status_code == 404:
                                        # 尝试备用路径格式 /api/assessments
                                        alt_api_url = f"{backup_url.replace('/api', '')}/api/assessments"
                                        logger.info(f"404错误，尝试备用API路径: {alt_api_url}")
                                        backup_response = requests.get(
                                            alt_api_url,
                                            params=params,
                                            headers=backup_headers,
                                            timeout=10,
                                            proxies={
                                                'http': None,
                                                'https': None
                                            }
                                        )

                                        # 如果仍然失败，尝试第三种路径
                                        if backup_response.status_code == 404:
                                            alt_api_url = f"{backup_url.replace('/api', '')}/api/v1/assessments"
                                            logger.info(f"404错误，尝试第三种API路径: {alt_api_url}")
                                            backup_response = requests.get(
                                                alt_api_url,
                                                params=params,
                                                headers=backup_headers,
                                                timeout=10,
                                                proxies={
                                                    'http': None,
                                                    'https': None
                                                }
                                            )

                                    if backup_response.status_code == 200:
                                        if backup_response.text.strip():
                                            try:
                                                backup_result = backup_response.json()
                                                logger.info("成功从备用服务器获取数据")

                                                # 检查数据格式，判断是否为列表
                                                if isinstance(backup_result, list):
                                                    # 如果是列表，直接作为数据
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                                elif isinstance(backup_result, dict):
                                                    # 如果是字典，使用原格式
                                                    result = backup_result
                                                else:
                                                    # 其他情况，包装为标准格式
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                            except ValueError:
                                                logger.error(f"备用服务器响应不是有效的JSON: {backup_response.text[:100]}")
                                                result = {"status": "error", "message": "备用服务器返回的数据格式不正确"}
                                        else:
                                            logger.error("备用服务器返回空响应")
                                            result = {"status": "error", "message": "备用服务器返回空响应"}
                                    else:
                                        logger.error(f"备用服务器也返回错误状态码: {backup_response.status_code}")
                                        result = {"status": "error", "message": "无法从主服务器和备用服务器获取量表详情"}
                                except Exception as backup_error:
                                    logger.error(f"尝试使用备用服务器时出错: {str(backup_error)}")
                                    result = {"status": "error", "message": f"备用服务器请求失败: {str(backup_error)}"}
                            else:
                                result = {"status": "error", "message": f"服务器返回错误状态码: {response.status_code}"}
                except Exception as api_error:
                    logger.error(f"API调用出错: {str(api_error)}")
                    error_msg = str(api_error)

                    # 检查是否是代理相关错误
                    if "proxy" in error_msg.lower() or "127.0.0.1:7890" in error_msg:
                        logger.warning("检测到代理相关错误，尝试修复代理设置")
                        try:
                            from utils.proxy_config import fix_proxy_issues
                            if fix_proxy_issues():
                                logger.info("代理问题已修复，建议重新尝试")
                                error_msg = "网络连接问题已修复，请重新尝试获取评估量表"
                            else:
                                error_msg = "检测到代理连接问题，请检查网络设置"
                        except Exception as proxy_fix_error:
                            logger.error(f"修复代理设置失败: {proxy_fix_error}")
                            error_msg = "网络连接问题，请检查网络设置或联系技术支持"

                    result = {"status": "error", "message": error_msg}

                # 处理结果
                if result:
                    # 添加调试日志
                    logger.info(f"API返回结果类型: {type(result)}")
                    if isinstance(result, dict):
                        logger.info(f"结果字典键: {list(result.keys())}")
                    elif isinstance(result, list):
                        logger.info(f"结果列表长度: {len(result)}")
                        if result:
                            logger.info(f"第一个元素类型: {type(result[0])}")

                    # 检查结果格式并提取数据
                    if isinstance(result, list):
                        # 如果直接返回列表，过滤掉非字典项
                        filtered_list = []
                        for item in result:
                            if isinstance(item, dict):
                                filtered_list.append(item)
                            else:
                                logger.warning(f"跳过非字典类型的评估量表项: {type(item)} - {item}")

                        self.assessment_list = filtered_list
                        logger.info(f"获取到 {len(filtered_list)} 个有效评估量表")

                        # 添加调试日志：显示第一个评估量表的字段
                        if filtered_list:
                            first_assessment = filtered_list[0]
                            logger.info(f"第一个评估量表的字段: {list(first_assessment.keys())}")
                            logger.info(f"第一个评估量表的ID相关字段: id={first_assessment.get('id')}, assessment_id={first_assessment.get('assessment_id')}, template_id={first_assessment.get('template_id')}")

                        Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
                        # 及时关闭加载对话框
                        Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                    elif isinstance(result, dict):
                        if result.get('status') == 'success':
                            # 标准成功响应格式
                            data = result.get('data', [])

                            # 检查是否是分页格式（包含assessments字段）
                            if isinstance(data, dict) and 'assessments' in data:
                                # 分页格式：data.assessments包含实际的评估量表列表
                                assessments = data.get('assessments', [])
                                logger.info(f"检测到分页格式，评估量表在assessments字段中，数量: {len(assessments)}")

                                # 过滤掉非字典项并去重
                                filtered_list = []
                                seen_names = set()  # 用于去重的集合

                                for item in assessments:
                                    if isinstance(item, dict):
                                        # 使用量表名称进行去重
                                        name = item.get('name', '')
                                        if name and name not in seen_names:
                                            filtered_list.append(item)
                                            seen_names.add(name)
                                            logger.info(f"添加量表: {name} (ID: {item.get('id')})")
                                        elif name in seen_names:
                                            logger.info(f"跳过重复量表: {name} (ID: {item.get('id')})")
                                        else:
                                            logger.warning(f"跳过无名称量表: {item}")
                                    else:
                                        logger.warning(f"跳过非字典类型的评估量表项: {type(item)} - {item}")

                                self.assessment_list = filtered_list
                                logger.info(f"获取到 {len(filtered_list)} 个有效评估量表（已去重）")
                            elif isinstance(data, list):
                                # 直接列表格式
                                filtered_list = []
                                for item in data:
                                    if isinstance(item, dict):
                                        filtered_list.append(item)
                                    else:
                                        logger.warning(f"跳过非字典类型的评估量表项: {type(item)} - {item}")

                                self.assessment_list = filtered_list
                                logger.info(f"获取到 {len(filtered_list)} 个有效评估量表")
                            else:
                                # 如果data不是列表也不是包含assessments的字典，包装为列表
                                if isinstance(data, dict):
                                    self.assessment_list = [data]
                                    logger.info("获取到 1 个评估量表")
                                else:
                                    logger.warning(f"数据格式异常: {type(data)} - {data}")
                                    self.assessment_list = []

                            Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
                        else:
                            # 错误响应
                            error_msg = result.get('message', '未知错误')
                            logger.error(f"获取评估量表失败: {error_msg}")
                            error_message = f"获取评估量表失败: {error_msg}"
                            Clock.schedule_once(lambda dt: self.show_error(error_message), 0)
                    else:
                        # 其他格式，尝试直接使用
                        if isinstance(result, dict):
                            self.assessment_list = [result]
                        else:
                            logger.warning(f"未知的结果格式: {type(result)} - {result}")
                            self.assessment_list = []
                        Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
                else:
                    error_msg = '请求失败'
                    logger.error(f"获取评估量表失败: {error_msg}")
                    # 云端获取失败，尝试使用本地分发数据作为备用
                    logger.info("云端获取失败，尝试使用本地分发数据作为备用")
                    self._try_load_local_assessments(custom_id)
                    return
            else:
                logger.error("API客户端未初始化，尝试使用本地分发数据")
                # API客户端未初始化，尝试使用本地分发数据作为备用
                self._try_load_local_assessments(custom_id)
                return
        except Exception as e:
            logger.error(f"获取评估量表时出错: {str(e)}")
            error_message = f"获取评估量表失败: {str(e)}"
            Clock.schedule_once(lambda dt: self.show_error(error_message), 0)
        finally:
            # 关闭加载对话框
            logger.info("准备关闭评估量表加载对话框")
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)

    def _try_load_local_assessments(self, custom_id):
        """尝试从本地分发管理器加载评估量表作为备用"""
        try:
            from utils.distribution_manager import get_distribution_manager
            distribution_manager = get_distribution_manager()
            local_assessments = distribution_manager.get_pending_assessments(custom_id)

            if local_assessments:
                logger.info(f"从本地分发管理器获取到 {len(local_assessments)} 个评估量表作为备用")
                self.assessment_list = local_assessments
                Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
            else:
                logger.info("本地分发管理器中也没有评估量表数据")
                self.assessment_list = []
                Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
                Clock.schedule_once(lambda dt: self.show_error("无法获取评估量表，请检查网络连接或联系管理员"), 0)
        except Exception as e:
            logger.error(f"从本地加载评估量表失败: {e}")
            self.assessment_list = []
            Clock.schedule_once(lambda dt: self.refresh_assessment_list(), 0)
            Clock.schedule_once(lambda dt: self.show_error("无法获取评估量表，请检查网络连接或联系管理员"), 0)

    def refresh_assessment_list(self):
        """刷新评估量表列表UI"""
        try:
            logger.info(f"开始刷新评估量表列表UI，当前量表数量: {len(self.assessment_list) if hasattr(self, 'assessment_list') and self.assessment_list else 0}")
            alist = self.ids.assessment_list
            alist.clear_widgets()

            if not self.assessment_list:
                # 添加提示信息
                item = MDListItem(
                    MDListItemHeadlineText(
                        text="暂无评估量表",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.PRIMARY_DARK
                    ),
                    MDListItemSupportingText(
                        text="请稍后刷新或联系健康顾问",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR
                )
                alist.add_widget(item)
                return

            for a in self.assessment_list:
                # 获取量表信息，尝试不同的字段名
                title = a.get('name') or a.get('title') or a.get('assessment_name') or '未命名量表'
                description = a.get('notes') or a.get('description') or a.get('summary') or '无描述'

                # 如果描述太长，截断显示
                if len(description) > 50:
                    description = description[:50] + "..."

                # 创建列表项
                item = MDListItem(
                    MDListItemHeadlineText(
                        text=title,
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    MDListItemSupportingText(
                        text=description,
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR,
                    size_hint_y=None,
                    height="80dp"
                )
                item.bind(on_release=partial(self.open_assessment_form, a))
                alist.add_widget(item)
        except Exception as e:
            logger.error(f"刷新评估量表列表时出错: {str(e)}")
            self.show_error(f"刷新评估量表列表失败: {str(e)}")

    def load_questionnaires(self):
        """加载问卷列表"""
        try:
            # 显示加载对话框
            self.show_loading_dialog("正在加载问卷...")

            # 在后台线程中加载数据
            threading.Thread(target=self._fetch_questionnaires_in_background, daemon=True).start()
        except Exception as e:
            logger.error(f"加载问卷列表时出错: {str(e)}")
            self.dismiss_loading_dialog()
            self.show_error(f"加载问卷列表失败: {str(e)}")

    def _fetch_questionnaires_in_background(self):
        """在后台线程中从API获取问卷"""
        try:
            # 获取用户信息
            user_info = self.auth_manager.get_current_user_info()
            custom_id = user_info.get('custom_id')

            if not custom_id:
                logger.warning("无法获取用户custom_id")
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                Clock.schedule_once(lambda dt: self.show_error("用户信息不完整，请重新登录"), 0)
                return

            # 首先尝试从云端API获取问卷
            if self.api_client and hasattr(self.api_client, 'cloud_api'):
                try:
                    # 使用新的移动端API方法
                    if hasattr(self.api_client.cloud_api, 'get_mobile_questionnaires'):
                        result = self.api_client.cloud_api.get_mobile_questionnaires(custom_id)
                    else:
                        # 如果没有新方法，回退到旧的API调用
                        logger.warning("云端API不支持get_mobile_questionnaires方法，使用旧的API")
                        result = {"status": "error", "message": "API方法不支持"}

                    # 处理结果
                    if result and (result.get('status') == 'success' or result.get('success')):
                        # 提取数据
                        data = result.get('data', [])
                        if isinstance(data, dict) and 'questionnaires' in data:
                            questionnaires = data.get('questionnaires', [])
                        elif isinstance(data, list):
                            questionnaires = data
                        else:
                            questionnaires = []

                        # 过滤掉非字典项并去重
                        filtered_list = []
                        seen_names = set()

                        for item in questionnaires:
                            if isinstance(item, dict):
                                # 使用问卷名称进行去重
                                name = item.get('name') or item.get('title') or ''
                                if name and name not in seen_names:
                                    filtered_list.append(item)
                                    seen_names.add(name)
                                    logger.info(f"添加问卷: {name} (ID: {item.get('id')})")
                                elif name in seen_names:
                                    logger.info(f"跳过重复问卷: {name} (ID: {item.get('id')})")
                                else:
                                    logger.warning(f"跳过无名称问卷: {item}")
                            else:
                                logger.warning(f"跳过非字典类型的问卷项: {type(item)} - {item}")

                        self.questionnaire_list = filtered_list
                        logger.info(f"从云端获取到 {len(filtered_list)} 个有效问卷")
                        Clock.schedule_once(lambda dt: self.refresh_questionnaire_list(), 0)
                        # 及时关闭加载对话框
                        Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                    else:
                        # 云端获取失败，尝试使用本地分发数据作为备用
                        error_msg = result.get('message', '获取问卷失败') if result else '请求失败'
                        logger.error(f"获取移动端问卷失败: {error_msg}")
                        logger.info("云端获取失败，尝试使用本地分发数据作为备用")
                        self._try_load_local_questionnaires(custom_id)
                        return
                except Exception as e:
                    logger.error(f"获取问卷时出错: {str(e)}")
                    # 云端获取失败，尝试使用本地分发数据作为备用
                    logger.info("云端获取失败，尝试使用本地分发数据作为备用")
                    self._try_load_local_questionnaires(custom_id)
                    return
            else:
                logger.error("API客户端未初始化，尝试使用本地分发数据")
                # API客户端未初始化，尝试使用本地分发数据作为备用
                self._try_load_local_questionnaires(custom_id)
                return
        except Exception as e:
            logger.error(f"获取问卷时出错: {str(e)}")
            error_message = f"获取问卷失败: {str(e)}"
            Clock.schedule_once(lambda dt: self.show_error(error_message), 0)
        finally:
            # 关闭加载对话框
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)

    def _try_load_local_questionnaires(self, custom_id):
        """尝试从本地分发管理器加载问卷作为备用"""
        try:
            from utils.distribution_manager import get_distribution_manager
            distribution_manager = get_distribution_manager()
            local_questionnaires = distribution_manager.get_pending_questionnaires(custom_id)

            if local_questionnaires:
                logger.info(f"从本地分发管理器获取到 {len(local_questionnaires)} 个问卷作为备用")
                self.questionnaire_list = local_questionnaires
                Clock.schedule_once(lambda dt: self.refresh_questionnaire_list(), 0)
            else:
                logger.info("本地分发管理器中也没有问卷数据")
                # 如果本地分发管理器也没有数据，尝试从本地问卷管理器获取
                try:
                    manager = get_survey_manager()
                    self.questionnaire_list = manager.list_available_questionnaires(user_id=custom_id)
                    logger.info(f"从本地问卷管理器获取到 {len(self.questionnaire_list)} 个问卷")
                    Clock.schedule_once(lambda dt: self.refresh_questionnaire_list(), 0)
                except Exception as local_error:
                    logger.error(f"从本地问卷管理器获取问卷失败: {local_error}")
                    self.questionnaire_list = []
                    Clock.schedule_once(lambda dt: self.refresh_questionnaire_list(), 0)
                    Clock.schedule_once(lambda dt: self.show_error("无法获取问卷，请检查网络连接或联系管理员"), 0)
        except Exception as e:
            logger.error(f"从本地加载问卷失败: {e}")
            self.questionnaire_list = []
            Clock.schedule_once(lambda dt: self.refresh_questionnaire_list(), 0)
            Clock.schedule_once(lambda dt: self.show_error("无法获取问卷，请检查网络连接或联系管理员"), 0)

    def load_response_history(self):
        """加载历史答卷"""
        try:
            # 获取用户信息
            user_info = self.auth_manager.get_current_user_info()
            custom_id = user_info.get('custom_id')

            if not custom_id:
                logger.warning("无法获取用户custom_id，使用默认ID")
                custom_id = "default_user"

            manager = get_survey_manager()
            self.response_history = manager.list_user_responses(user_id=custom_id)
            self.refresh_response_history_list()
        except Exception as e:
            logger.error(f"加载历史答卷时出错: {str(e)}")
            self.show_error(f"加载历史答卷失败: {str(e)}")

    def refresh_questionnaire_list(self):
        """刷新问卷列表UI"""
        try:
            qlist = self.ids.questionnaire_list
            qlist.clear_widgets()

            if not self.questionnaire_list:
                # 添加提示信息
                item = MDListItem(
                    MDListItemHeadlineText(
                        text="暂无问卷",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.PRIMARY_DARK
                    ),
                    MDListItemSupportingText(
                        text="请稍后刷新或联系健康顾问",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR
                )
                qlist.add_widget(item)
                return

            for q in self.questionnaire_list:
                item = MDListItem(
                    MDListItemHeadlineText(
                        text=q.get('title', '未命名问卷'),
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR
                )
                item.bind(on_release=partial(self.open_questionnaire_form, q))
                qlist.add_widget(item)
        except Exception as e:
            logger.error(f"刷新问卷列表时出错: {str(e)}")
            self.show_error(f"刷新问卷列表失败: {str(e)}")

    def refresh_response_history_list(self):
        """刷新历史答卷列表UI"""
        try:
            rlist = self.ids.response_history_list
            rlist.clear_widgets()

            if not self.response_history:
                # 添加提示信息
                item = MDListItem(
                    MDListItemHeadlineText(
                        text="暂无历史记录",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.PRIMARY_DARK
                    ),
                    MDListItemSupportingText(
                        text="完成评估量表或问卷后将显示在此处",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR
                )
                rlist.add_widget(item)
                return

            for resp in self.response_history:
                title = resp.get('questionnaire_title', '未命名问卷')
                submit_time = resp.get('submitted_at', '')
                item = MDListItem(
                    MDListItemHeadlineText(
                        text=f"{title}",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    MDListItemSupportingText(
                        text=f"提交时间: {submit_time}",
                        theme_text_color="Custom",
                        text_color=App.get_running_app().theme.TEXT_PRIMARY
                    ),
                    radius=12,
                    elevation=1,
                    divider_color=App.get_running_app().theme.DIVIDER_COLOR
                )
                item.bind(on_release=partial(self.show_response_detail, resp))
                rlist.add_widget(item)
        except Exception as e:
            logger.error(f"刷新历史答卷列表时出错: {str(e)}")
            self.show_error(f"刷新历史答卷列表失败: {str(e)}")

    def show_loading_dialog(self, text="加载中..."):
        """显示加载对话框"""
        from kivymd.uix.progressindicator import MDCircularProgressIndicator
        from kivymd.uix.dialog import MDDialogContentContainer

        content = MDBoxLayout(
            orientation='vertical',
            spacing=10,
            padding=20,
            adaptive_height=True
        )

        spinner = MDCircularProgressIndicator(
            size_hint=(None, None),
            size=(46, 46),
            pos_hint={"center_x": 0.5}
        )

        label = MDLabel(
            text=text,
            halign="center",
            valign="middle",
            size_hint_y=None,
            height=40
        )

        content.add_widget(spinner)
        content.add_widget(label)

        self.loading_dialog = MDDialog(
            MDDialogContentContainer(
                content,
                orientation='vertical'
            ),
            radius=[20, 7, 20, 7]
        )
        self.loading_dialog.open()

    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        logger.info("尝试关闭加载对话框")
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            logger.info("找到加载对话框，正在关闭")
            self.loading_dialog.dismiss()
            self.loading_dialog = None
            logger.info("加载对话框已关闭")
        else:
            logger.info("没有找到需要关闭的加载对话框")

    def open_assessment_form(self, assessment, *args):
        """打开评估量表填写表单"""
        try:
            # 保存当前选中的量表
            self.selected_assessment = assessment

            # 根据mobile_distribution_format.md规范获取量表ID
            # 优先使用id字段，然后是distribution_id
            assessment_id = assessment.get('id') or assessment.get('distribution_id')

            logger.info(f"获取到的量表ID: {assessment_id}")
            logger.info(f"量表数据字段: {list(assessment.keys())}")

            # 检查是否有template字段（按文档规范）
            if 'template' in assessment:
                logger.info(f"发现template字段，包含: {list(assessment['template'].keys())}")

            if not assessment_id:
                logger.error(f"无法获取量表ID，数据结构: {assessment}")
                self.dismiss_loading_dialog()
                self.show_error("量表数据格式错误，无法获取ID")
                return

            # 显示加载对话框
            self.show_loading_dialog("正在加载量表内容...")

            # 在后台线程中获取量表详情
            threading.Thread(target=self._fetch_assessment_detail_in_background, args=(assessment_id,), daemon=True).start()
        except Exception as e:
            logger.error(f"打开评估量表表单时出错: {str(e)}")
            self.dismiss_loading_dialog()
            self.show_error(f"无法打开评估量表: {str(e)}")

    def _fetch_assessment_detail_in_background(self, assessment_id):
        """在后台线程中获取量表详情"""
        try:
            # 获取用户信息
            user_info = self.auth_manager.get_current_user_info()
            custom_id = user_info.get('custom_id')

            if not custom_id:
                logger.warning("无法获取用户custom_id")
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                Clock.schedule_once(lambda dt: self.show_error("用户信息不完整，请重新登录"), 0)
                return

            # 首先尝试从分发管理器获取本地缓存的量表详情
            from utils.distribution_manager import get_distribution_manager
            distribution_manager = get_distribution_manager()

            # 检查是否是分发的评估量表
            pending_assessments = distribution_manager.get_pending_assessments(custom_id)
            assessment_detail = None

            for assessment in pending_assessments:
                if str(assessment.get('id')) == str(assessment_id):
                    assessment_detail = assessment
                    logger.info(f"从分发管理器获取到量表详情: {assessment.get('name', '未命名')}")
                    break

            if assessment_detail:
                # 如果找到分发的量表，直接使用本地数据
                Clock.schedule_once(lambda dt: self._show_assessment_form(assessment_detail), 0)
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                return

            # 如果不是分发的量表，从云端API获取
            logger.info(f"量表 {assessment_id} 不在分发列表中，尝试从云端获取")

            # 调用API获取量表详情
            if self.api_client and hasattr(self.api_client, 'cloud_api') and self.api_client.cloud_api:
                # 确保cloud_api中也设置了custom_id
                if not hasattr(self.api_client.cloud_api, 'custom_id') or not self.api_client.cloud_api.custom_id:
                    self.api_client.cloud_api.custom_id = custom_id
                    logger.info(f"设置cloud_api的custom_id: {custom_id}")
                    # 保存认证信息
                    if hasattr(self.api_client.cloud_api, 'save_auth_info'):
                        self.api_client.cloud_api.save_auth_info()

                # 强制刷新认证管理器中的用户信息
                self.auth_manager.refresh_user_info()

                # 使用认证管理器获取请求头
                headers = self.auth_manager.get_auth_headers()

                # 确保请求头中包含必要的认证信息
                if 'Authorization' not in headers and hasattr(self.api_client.cloud_api, 'token') and self.api_client.cloud_api.token:
                    headers['Authorization'] = f"Bearer {self.api_client.cloud_api.token}"

                if 'X-User-ID' not in headers and custom_id:
                    headers['X-User-ID'] = custom_id

                logger.info(f"发送量表详情请求，认证头: {headers.get('Authorization', '无')[:15] if headers.get('Authorization') else '无'}..., X-User-ID: {headers.get('X-User-ID', '无')}")

                # 构建请求参数
                params = {'custom_id': custom_id}

                try:
                    # 调用API
                    logger.info(f"获取量表详情，ID: {assessment_id}, custom_id: {custom_id}")

                    # 尝试使用make_request方法
                    if hasattr(self.api_client.cloud_api, 'make_request'):
                        result = self.api_client.cloud_api.make_request(
                            method="GET",
                            endpoint=f"assessments/{assessment_id}",
                            params=params,
                            headers=headers
                        )
                    else:
                        # 如果没有make_request方法，使用requests库直接请求
                        import requests
                        base_url = getattr(self.api_client.cloud_api, 'base_url', 'http://localhost:8006/api')

                        # 确保URL格式正确
                        if base_url.endswith('/api'):
                            url = f"{base_url}/assessments/{assessment_id}"
                        else:
                            url = f"{base_url}/api/assessments/{assessment_id}"

                        # 发送请求
                        logger.info(f"使用requests库获取量表详情，URL: {url}")
                        response = requests.get(
                            url,
                            params=params,
                            headers=headers,
                            timeout=10,
                            proxies={
                                'http': None,
                                'https': None
                            }
                        )

                        # 检查响应状态码
                        if response.status_code == 200:
                            # 检查响应内容是否为空
                            if response.text.strip():
                                try:
                                    raw_result = response.json()
                                    logger.info(f"量表详情服务器返回数据类型: {type(raw_result)}")

                                    # 调试：打印完整的响应数据结构
                                    import json
                                    logger.info(f"量表详情完整响应数据: {json.dumps(raw_result, ensure_ascii=False, indent=2)}")

                                    # 检查数据格式，判断是否为字典（量表详情应该是单个对象）
                                    if isinstance(raw_result, dict):
                                        # 如果是字典，检查是否已经是标准格式
                                        if 'status' in raw_result or 'data' in raw_result:
                                            result = raw_result
                                        else:
                                            # 如果是普通字典（直接的量表详情），包装为标准格式
                                            result = {
                                                "status": "success",
                                                "data": raw_result
                                            }
                                            logger.info("量表详情：直接返回字典格式，已包装为标准格式")
                                    elif isinstance(raw_result, list):
                                        # 如果是列表，取第一个元素作为详情
                                        if raw_result:
                                            result = {
                                                "status": "success",
                                                "data": raw_result[0]
                                            }
                                            logger.info("量表详情：返回列表格式，取第一个元素")
                                        else:
                                            result = {"status": "error", "message": "服务器返回空列表"}
                                    else:
                                        # 其他情况，包装为标准格式
                                        result = {
                                            "status": "success",
                                            "data": raw_result
                                        }
                                except ValueError:
                                    logger.error(f"响应不是有效的JSON: {response.text[:100]}")
                                    result = {"status": "error", "message": "服务器返回的数据格式不正确"}
                            else:
                                logger.error("服务器返回空响应")
                                result = {"status": "error", "message": "服务器返回空响应"}
                        else:
                            logger.error(f"服务器返回错误状态码: {response.status_code}")

                            # 如果是服务器错误，尝试使用备用服务器
                            if response.status_code >= 500:
                                logger.info("主服务器错误，尝试使用备用服务器")

                                try:
                                    # 从配置中获取备用URL
                                    from utils.app_config import API_CONFIG
                                    backup_url = API_CONFIG.get('BACKUP_URL', 'http://localhost:8006/api')

                                    # 构建备用URL（使用assessment_id，因为是获取详情）
                                    backup_api_url = f"{backup_url}/assessments/{assessment_id}"

                                    # 强制刷新认证管理器获取最新token
                                    self.auth_manager.refresh_user_info()
                                    backup_headers = self.auth_manager.get_auth_headers()

                                    # 确保包含X-User-ID
                                    if 'X-User-ID' not in backup_headers and custom_id:
                                        backup_headers['X-User-ID'] = custom_id

                                    # 添加其他可能的认证头
                                    backup_headers['Content-Type'] = 'application/json'

                                    logger.info(f"尝试使用备用服务器获取量表详情: {backup_api_url}，带认证头: {backup_headers.get('Authorization', '无')[:15] if backup_headers.get('Authorization') else '无'}..., X-User-ID: {backup_headers.get('X-User-ID', '无')}")
                                    backup_response = requests.get(
                                        backup_api_url,
                                        params=params,
                                        headers=backup_headers,
                                        timeout=10,
                                        proxies={
                                            'http': None,
                                            'https': None
                                        }
                                    )

                                    # 如果第一个路径失败，尝试其他可能的API路径
                                    if backup_response.status_code == 404:
                                        # 尝试备用路径格式 /api/assessments/{id}
                                        alt_api_url = f"{backup_url.replace('/api', '')}/api/assessments/{assessment_id}"
                                        logger.info(f"404错误，尝试备用API路径: {alt_api_url}")
                                        backup_response = requests.get(
                                            alt_api_url,
                                            params=params,
                                            headers=backup_headers,
                                            timeout=10,
                                            proxies={
                                                'http': None,
                                                'https': None
                                            }
                                        )

                                        # 如果仍然失败，尝试第三种路径
                                        if backup_response.status_code == 404:
                                            alt_api_url = f"{backup_url.replace('/api', '')}/api/v1/assessments/{assessment_id}"
                                            logger.info(f"404错误，尝试第三种API路径: {alt_api_url}")
                                            backup_response = requests.get(
                                                alt_api_url,
                                                params=params,
                                                headers=backup_headers,
                                                timeout=10,
                                                proxies={
                                                    'http': None,
                                                    'https': None
                                                }
                                            )

                                    if backup_response.status_code == 200:
                                        if backup_response.text.strip():
                                            try:
                                                backup_result = backup_response.json()
                                                logger.info("成功从备用服务器获取数据")

                                                # 检查数据格式，判断是否为列表
                                                if isinstance(backup_result, list):
                                                    # 如果是列表，直接作为数据
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                                elif isinstance(backup_result, dict):
                                                    # 如果是字典，使用原格式
                                                    result = backup_result
                                                else:
                                                    # 其他情况，包装为标准格式
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                            except ValueError:
                                                logger.error(f"备用服务器响应不是有效的JSON: {backup_response.text[:100]}")
                                                result = {"status": "error", "message": "备用服务器返回的数据格式不正确"}
                                        else:
                                            logger.error("备用服务器返回空响应")
                                            result = {"status": "error", "message": "备用服务器返回空响应"}
                                    else:
                                        logger.error(f"备用服务器也返回错误状态码: {backup_response.status_code}")
                                        result = {"status": "error", "message": "无法从主服务器和备用服务器获取量表详情"}
                                except Exception as backup_error:
                                    logger.error(f"尝试使用备用服务器时出错: {str(backup_error)}")
                                    result = {"status": "error", "message": f"备用服务器请求失败: {str(backup_error)}"}
                            else:
                                result = {"status": "error", "message": f"服务器返回错误状态码: {response.status_code}"}
                except Exception as api_error:
                    logger.error(f"API调用出错: {str(api_error)}")
                    result = {"status": "error", "message": str(api_error)}

                if result and result.get('status') == 'success':
                    # 获取量表详情
                    assessment_detail = result.get('data', {})

                    # 检查数据是否有效
                    if not assessment_detail:
                        logger.error("获取到的量表详情为空")
                        Clock.schedule_once(lambda dt: self.show_error("获取到的量表详情为空"), 0)
                        Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                        return

                    # 在主线程中显示量表表单
                    Clock.schedule_once(lambda dt: self._show_assessment_form(assessment_detail), 0)
                else:
                    error_msg = result.get('message', '未知错误') if result else '请求失败'
                    logger.error(f"获取量表详情失败: {error_msg}")
                    Clock.schedule_once(lambda dt: self.show_error(f"获取量表详情失败: {error_msg}"), 0)
            else:
                logger.error("API客户端未初始化")
                Clock.schedule_once(lambda dt: self.show_error("API客户端未初始化，无法获取量表详情"), 0)
        except Exception as e:
            logger.error(f"获取量表详情时出错: {str(e)}")
            Clock.schedule_once(lambda dt: self.show_error(f"获取量表详情失败: {str(e)}"), 0)
        finally:
            # 关闭加载对话框
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)

    def _show_assessment_form(self, assessment_detail):
        """显示量表填写表单"""
        try:
            # 根据mobile_distribution_format.md规范处理数据
            logger.info(f"量表详情字段: {list(assessment_detail.keys())}")

            # 检查是否有template字段（按文档规范）
            if 'template' in assessment_detail:
                # 使用template中的数据
                template_data = assessment_detail['template']
                logger.info(f"发现template字段，包含: {list(template_data.keys())}")

                assessment_name = template_data.get('name') or assessment_detail.get('name') or '未命名量表'
                assessment_description = template_data.get('description') or assessment_detail.get('description') or '无描述'
                questions = template_data.get('questions', [])
                logger.info(f"使用template数据，量表名称: {assessment_name}, 题目数量: {len(questions)}")
            else:
                # 直接使用assessment_detail中的数据
                assessment_name = assessment_detail.get('name') or assessment_detail.get('title') or '未命名量表'
                assessment_description = assessment_detail.get('description') or assessment_detail.get('notes') or '无描述'

                # 尝试从不同字段获取题目数据
                questions = None
                possible_question_fields = ['questions', 'items', 'scale_items', 'question_items', 'content']
                for field in possible_question_fields:
                    if field in assessment_detail and assessment_detail[field]:
                        questions = assessment_detail[field]
                        logger.info(f"找到题目数据在字段: {field}")
                        break

            if not questions:
                logger.error("未找到题目数据")
                logger.error(f"assessment_detail结构: {list(assessment_detail.keys())}")
                if 'template' in assessment_detail:
                    logger.error(f"template结构: {list(assessment_detail['template'].keys())}")
                self.show_error("该量表没有题目数据")
                return

            logger.info(f"题目数量: {len(questions)}")
            if questions:
                logger.info(f"第一个题目的字段: {list(questions[0].keys()) if isinstance(questions[0], dict) else '非字典类型'}")

            # 创建滚动视图
            from kivy.uix.scrollview import ScrollView
            scroll_view = ScrollView(size_hint=(1, None), size=(400, 500))

            # 创建表单布局
            from kivy.uix.boxlayout import BoxLayout
            form_layout = BoxLayout(orientation='vertical', spacing=15, padding=20, size_hint_y=None)
            form_layout.bind(minimum_height=form_layout.setter('height'))

            # 添加量表标题
            from kivy.uix.label import Label
            title_label = Label(
                text=assessment_name,
                font_size='18sp',
                halign='center',
                valign='middle',
                size_hint_y=None,
                height=50,
                text_size=(380, 50),
                color=(0.2, 0.2, 0.2, 1)  # 深灰色
            )
            form_layout.add_widget(title_label)

            # 添加量表说明
            if assessment_description and assessment_description != '无描述':
                desc_label = Label(
                    text=assessment_description,
                    font_size='14sp',
                    size_hint_y=None,
                    height=60,
                    halign='center',
                    valign='middle',
                    text_size=(380, 60),
                    color=(0.4, 0.4, 0.4, 1)  # 中灰色
                )
                form_layout.add_widget(desc_label)

            # 创建答案字段列表
            answer_fields = []

            # 添加题目和选项
            for i, q in enumerate(questions):
                # 调试：打印每个题目的字段
                logger.info(f"题目 {i+1} 字段: {list(q.keys()) if isinstance(q, dict) else '非字典类型'}")

                # 尝试从不同字段获取题目文本
                question_text = ''
                possible_text_fields = ['question_text', 'text', 'title', 'content', 'description']
                for field in possible_text_fields:
                    if field in q and q[field]:
                        question_text = q[field]
                        break

                if not question_text:
                    logger.warning(f"题目 {i+1} 没有文本内容，跳过")
                    continue

                question_label = Label(
                    text=f"{i+1}. {question_text}",
                    size_hint_y=None,
                    height=40,
                    halign='left',
                    valign='middle',
                    text_size=(400, 40)
                )
                form_layout.add_widget(question_label)

                # 尝试从不同字段获取选项
                options = None
                possible_option_fields = ['options', 'choices', 'answers', 'scale_options']
                for field in possible_option_fields:
                    if field in q and q[field]:
                        options = q[field]
                        logger.info(f"题目 {i+1} 找到选项在字段: {field}")
                        break

                # 确保options不为None
                if options is None:
                    # 如果没有找到选项字段，尝试生成默认的量表选项
                    logger.info(f"题目 {i+1} 没有选项字段，生成默认量表选项")
                    options = [
                        {"label": "从不", "value": "1", "score": 1},
                        {"label": "有时", "value": "2", "score": 2},
                        {"label": "经常", "value": "3", "score": 3},
                        {"label": "总是", "value": "4", "score": 4}
                    ]

                # 如果仍然没有选项，跳过这个题目
                if not options:
                    logger.warning(f"题目 {i+1} 没有选项，跳过")
                    continue

                # 使用普通的Kivy组件
                from kivy.uix.checkbox import CheckBox
                from kivy.uix.label import Label
                from kivy.uix.boxlayout import BoxLayout

                option_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=len(options) * 40)
                radio_group = []

                for option in options:
                    option_text = option.get('label', '')
                    option_value = option.get('value', '')
                    option_score = option.get('score', 0)

                    # 创建复选框（用作单选按钮）
                    checkbox = CheckBox(
                        group=f"question_{q.get('question_id', i)}",
                        size_hint=(None, None),
                        size=(48, 48),
                        active=False
                    )
                    # 设置复选框的值
                    checkbox.value = option_value
                    checkbox.score = option_score
                    radio_group.append(checkbox)

                    # 创建标签
                    label = Label(
                        text=f"{option_text} ({option_score}分)",
                        size_hint_x=1,
                        halign='left',
                        valign='middle',
                        text_size=(None, 40)
                    )

                    # 添加到布局
                    option_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=40)
                    option_row.add_widget(checkbox)
                    option_row.add_widget(label)
                    option_layout.add_widget(option_row)

                form_layout.add_widget(option_layout)
                answer_fields.append((q, radio_group))

            # 设置表单布局高度
            form_layout.height = sum(c.height for c in form_layout.children)

            # 添加表单到滚动视图
            scroll_view.add_widget(form_layout)

            # 创建对话框
            def on_submit(instance):
                # 收集答案
                answers = []
                for q, radio_group in answer_fields:
                    selected_option = None
                    for checkbox in radio_group:
                        if checkbox.active:
                            selected_option = checkbox.value
                            break

                    answers.append({
                        'question_id': q.get('question_id', ''),
                        'question_text': q.get('question_text', ''),
                        'answer': selected_option
                    })

                # 创建提交数据 - 格式化为健康记录格式
                response_data = {
                    'record_type': 'assessment',
                    'assessment_id': assessment_detail.get('id', ''),
                    'assessment_title': assessment_detail.get('title', '未命名量表'),
                    'submitted_at': datetime.now().isoformat(),
                    'answers': answers,
                    'total_score': sum([
                        next((checkbox.score for checkbox in radio_group if checkbox.active), 0)
                        for _, radio_group in answer_fields
                    ])
                }

                # 提交答案
                self.submit_assessment_result(response_data, assessment_detail)
                dialog.dismiss()

            from kivy.uix.popup import Popup
            from kivy.uix.button import Button

            # 创建按钮布局
            button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)
            submit_button = Button(text='提交')
            cancel_button = Button(text='取消')
            button_layout.add_widget(submit_button)
            button_layout.add_widget(cancel_button)

            # 创建内容布局
            content_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
            content_layout.add_widget(scroll_view)
            content_layout.add_widget(button_layout)

            # 创建弹窗
            dialog = Popup(
                title=assessment_detail.get('title', '评估量表'),
                content=content_layout,
                size_hint=(0.9, 0.9),
                auto_dismiss=False
            )

            # 绑定按钮事件
            submit_button.bind(on_release=on_submit)
            cancel_button.bind(on_release=dialog.dismiss)
            dialog.open()
        except Exception as e:
            logger.error(f"显示量表表单时出错: {str(e)}")
            self.show_error(f"无法显示量表表单: {str(e)}")

    def submit_assessment_result(self, response_data, assessment_detail):
        """提交评估量表结果到后端"""
        try:
            # 显示加载对话框
            self.show_loading_dialog("正在提交评估结果...")

            # 在后台线程中提交数据
            threading.Thread(target=self._submit_assessment_result_in_background, args=(response_data, assessment_detail), daemon=True).start()
        except Exception as e:
            logger.error(f"提交评估量表结果时出错: {str(e)}")
            self.dismiss_loading_dialog()
            self.show_error(f"提交评估结果失败: {str(e)}")

    def _submit_assessment_result_in_background(self, response_data, assessment_detail):
        """在后台线程中提交评估量表结果"""
        try:
            # 获取用户信息
            user_info = self.auth_manager.get_current_user_info()
            custom_id = user_info.get('custom_id')

            if not custom_id:
                logger.warning("无法获取用户custom_id")
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                Clock.schedule_once(lambda dt: self.show_error("用户信息不完整，请重新登录"), 0)
                return

            # 准备符合移动端分发格式的答案数据
            # 优先使用distribution_id，如果没有则使用id，最后使用template_id
            assessment_id = (assessment_detail.get('distribution_id') or
                           assessment_detail.get('id') or
                           assessment_detail.get('template_id'))

            # 如果仍然没有ID，尝试从template字段获取
            if not assessment_id and 'template' in assessment_detail:
                template = assessment_detail['template']
                assessment_id = template.get('id') or template.get('template_id')

            logger.info(f"使用assessment_id进行提交: {assessment_id}")

            if not assessment_id:
                logger.error("无法获取有效的assessment_id")
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                Clock.schedule_once(lambda dt: self.show_error("评估量表ID无效，无法提交"), 0)
                return

            # 转换答案格式为符合文档规范的格式
            formatted_answers = []
            for answer in response_data.get('answers', []):
                formatted_answer = {
                    'question_id': answer.get('question_id'),
                    'answer': answer.get('answer'),
                    'score': getattr(answer, 'score', 0) if hasattr(answer, 'score') else 0
                }
                formatted_answers.append(formatted_answer)

            # 调用新的移动端API提交评估结果
            if self.api_client and hasattr(self.api_client, 'cloud_api') and self.api_client.cloud_api:
                # 确保cloud_api中也设置了custom_id
                if not hasattr(self.api_client.cloud_api, 'custom_id') or not self.api_client.cloud_api.custom_id:
                    self.api_client.cloud_api.custom_id = custom_id
                    logger.info(f"设置cloud_api的custom_id: {custom_id}")
                    # 保存认证信息
                    if hasattr(self.api_client.cloud_api, 'save_auth_info'):
                        self.api_client.cloud_api.save_auth_info()

                # 强制刷新认证管理器中的用户信息
                self.auth_manager.refresh_user_info()

                # 使用认证管理器获取请求头
                headers = self.auth_manager.get_auth_headers()

                # 确保请求头中包含必要的认证信息
                if 'Authorization' not in headers and hasattr(self.api_client.cloud_api, 'token') and self.api_client.cloud_api.token:
                    headers['Authorization'] = f"Bearer {self.api_client.cloud_api.token}"

                if 'X-User-ID' not in headers and custom_id:
                    headers['X-User-ID'] = custom_id

                logger.info(f"发送评估结果提交请求，认证头: {headers.get('Authorization', '无')[:15] if headers.get('Authorization') else '无'}..., X-User-ID: {headers.get('X-User-ID', '无')}")

                try:
                    # 调用新的移动端API
                    logger.info(f"使用移动端API提交评估结果，assessment_id: {assessment_id}, custom_id: {custom_id}")

                    # 使用新的移动端API方法
                    if hasattr(self.api_client.cloud_api, 'submit_mobile_assessment'):
                        result = self.api_client.cloud_api.submit_mobile_assessment(
                            assessment_id, formatted_answers, custom_id
                        )
                    else:
                        # 如果没有新方法，回退到旧的API调用
                        logger.warning("云端API不支持submit_mobile_assessment方法，使用旧的API")

                        # 强制刷新认证管理器中的用户信息
                        self.auth_manager.refresh_user_info()

                        # 使用认证管理器获取请求头
                        headers = self.auth_manager.get_auth_headers()

                        # 确保请求头中包含必要的认证信息
                        if 'Authorization' not in headers and hasattr(self.api_client.cloud_api, 'token') and self.api_client.cloud_api.token:
                            headers['Authorization'] = f"Bearer {self.api_client.cloud_api.token}"

                        if 'X-User-ID' not in headers and custom_id:
                            headers['X-User-ID'] = custom_id

                        logger.info(f"发送评估结果提交请求，认证头: {headers.get('Authorization', '无')[:15] if headers.get('Authorization') else '无'}..., X-User-ID: {headers.get('X-User-ID', '无')}")

                        # 尝试使用make_request方法
                        if hasattr(self.api_client.cloud_api, 'make_request'):
                            result = self.api_client.cloud_api.make_request(
                                method="POST",
                                endpoint=f"mobile/assessments/{assessment_id}/submit",
                                json_data={'answers': formatted_answers},
                                headers=headers
                            )
                        else:
                            # 如果没有make_request方法，使用requests库直接请求
                            import requests
                            base_url = getattr(self.api_client.cloud_api, 'base_url', 'http://localhost:8006/api')

                        # 确保URL格式正确
                        if base_url.endswith('/api'):
                            url = f"{base_url}/user-health-records/{custom_id}"
                        else:
                            url = f"{base_url}/api/user-health-records/{custom_id}"

                        # 发送请求
                        logger.info(f"使用requests库提交评估结果，URL: {url}")
                        response = requests.post(
                            url,
                            json=response_data,
                            headers=headers,
                            timeout=10,
                            proxies={
                                'http': None,
                                'https': None
                            }
                        )

                        # 检查响应状态码
                        if response.status_code == 200:
                            # 检查响应内容是否为空
                            if response.text.strip():
                                try:
                                    result = response.json()
                                except ValueError:
                                    logger.error(f"响应不是有效的JSON: {response.text[:100]}")
                                    result = {"status": "error", "message": "服务器返回的数据格式不正确"}
                            else:
                                logger.error("服务器返回空响应")
                                result = {"status": "error", "message": "服务器返回空响应"}
                        else:
                            logger.error(f"服务器返回错误状态码: {response.status_code}")

                            # 如果是服务器错误，尝试使用备用服务器
                            if response.status_code >= 500:
                                logger.info("主服务器错误，尝试使用备用服务器")

                                try:
                                    # 从配置中获取备用URL
                                    from utils.app_config import API_CONFIG
                                    backup_url = API_CONFIG.get('BACKUP_URL', 'http://localhost:8006/api')

                                    # 构建备用URL（使用user-health-records路径）
                                    backup_api_url = f"{backup_url}/user-health-records/{custom_id}"

                                    # 强制刷新认证管理器获取最新token
                                    self.auth_manager.refresh_user_info()
                                    backup_headers = self.auth_manager.get_auth_headers()

                                    # 确保包含X-User-ID
                                    if 'X-User-ID' not in backup_headers and custom_id:
                                        backup_headers['X-User-ID'] = custom_id

                                    # 添加其他可能的认证头
                                    backup_headers['Content-Type'] = 'application/json'

                                    logger.info(f"尝试使用备用服务器提交评估结果: {backup_api_url}，带认证头: {backup_headers.get('Authorization', '无')[:15] if backup_headers.get('Authorization') else '无'}..., X-User-ID: {backup_headers.get('X-User-ID', '无')}")
                                    backup_response = requests.post(
                                        backup_api_url,
                                        json=response_data,
                                        headers=backup_headers,
                                        timeout=10,
                                        proxies={
                                            'http': None,
                                            'https': None
                                        }
                                    )

                                    # 如果第一个路径失败，尝试其他可能的API路径
                                    if backup_response.status_code == 404:
                                        # 尝试备用路径格式 /api/user-health-records
                                        alt_api_url = f"{backup_url.replace('/api', '')}/api/user-health-records/{custom_id}"
                                        logger.info(f"404错误，尝试备用API路径: {alt_api_url}")
                                        backup_response = requests.post(
                                            alt_api_url,
                                            json=response_data,
                                            headers=backup_headers,
                                            timeout=10,
                                            proxies={
                                                'http': None,
                                                'https': None
                                            }
                                        )

                                        # 如果仍然失败，尝试第三种路径（assessment-responses）
                                        if backup_response.status_code == 404:
                                            alt_api_url = f"{backup_url.replace('/api', '')}/api/assessment-responses"
                                            logger.info(f"404错误，尝试第三种API路径: {alt_api_url}")
                                            backup_response = requests.post(
                                                alt_api_url,
                                                json=response_data,
                                                headers=backup_headers,
                                                timeout=10,
                                                proxies={
                                                    'http': None,
                                                    'https': None
                                                }
                                            )

                                    if backup_response.status_code == 200:
                                        if backup_response.text.strip():
                                            try:
                                                backup_result = backup_response.json()
                                                logger.info("成功从备用服务器获取数据")

                                                # 检查数据格式，判断是否为列表
                                                if isinstance(backup_result, list):
                                                    # 如果是列表，直接作为数据
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                                elif isinstance(backup_result, dict):
                                                    # 如果是字典，使用原格式
                                                    result = backup_result
                                                else:
                                                    # 其他情况，包装为标准格式
                                                    result = {
                                                        "status": "success",
                                                        "data": backup_result
                                                    }
                                            except ValueError:
                                                logger.error(f"备用服务器响应不是有效的JSON: {backup_response.text[:100]}")
                                                result = {"status": "error", "message": "备用服务器返回的数据格式不正确"}
                                        else:
                                            logger.error("备用服务器返回空响应")
                                            result = {"status": "error", "message": "备用服务器返回空响应"}
                                    else:
                                        logger.error(f"备用服务器也返回错误状态码: {backup_response.status_code}")

                                        # 如果两个服务器都失败，则保存到本地
                                        try:
                                            from utils.survey_manager import get_survey_manager
                                            survey_manager = get_survey_manager()

                                            # 准备本地保存的数据
                                            local_data = {
                                                'assessment_id': response_data.get('assessment_id'),
                                                'assessment_title': '本地保存的评估结果',
                                                'custom_id': custom_id,
                                                'submitted_at': datetime.now().isoformat(),
                                                'answers': response_data.get('answers', [])
                                            }

                                            # 保存到本地
                                            survey_manager.save_assessment_response(local_data)
                                            logger.info("评估结果已保存到本地")
                                            result = {"status": "success", "message": "评估结果已保存到本地"}
                                        except Exception as save_error:
                                            logger.error(f"保存评估结果到本地时出错: {str(save_error)}")
                                            result = {"status": "error", "message": f"无法从服务器提交且本地保存失败: {str(save_error)}"}
                                except Exception as backup_error:
                                    logger.error(f"尝试使用备用服务器时出错: {str(backup_error)}")
                                    result = {"status": "error", "message": f"备用服务器请求失败: {str(backup_error)}"}
                            else:
                                result = {"status": "error", "message": f"服务器返回错误状态码: {response.status_code}"}
                except Exception as api_error:
                    logger.error(f"API调用出错: {str(api_error)}")
                    result = {"status": "error", "message": str(api_error)}

                if result and result.get('status') == 'success':
                    # 提交成功，标记评估量表为已完成
                    from utils.distribution_manager import get_distribution_manager
                    distribution_manager = get_distribution_manager()
                    distribution_manager.mark_assessment_completed(
                        assessment_id,
                        {
                            'total_score': response_data.get('total_score', 0),
                            'answers_count': len(formatted_answers),
                            'submitted_at': datetime.now().isoformat()
                        }
                    )

                    Clock.schedule_once(lambda dt: self.show_info("评估结果提交成功！"), 0)
                    # 刷新历史记录和评估量表列表
                    Clock.schedule_once(lambda dt: self.load_response_history(), 0)
                    Clock.schedule_once(lambda dt: self.load_assessments(), 0)
                else:
                    error_msg = result.get('message', '未知错误') if result else '请求失败'
                    logger.error(f"提交评估结果失败: {error_msg}")
                    Clock.schedule_once(lambda dt: self.show_error(f"提交评估结果失败: {error_msg}"), 0)
            else:
                logger.error("API客户端未初始化")
                Clock.schedule_once(lambda dt: self.show_error("API客户端未初始化，无法提交评估结果"), 0)
        except Exception as e:
            logger.error(f"提交评估结果时出错: {str(e)}")
            Clock.schedule_once(lambda dt: self.show_error(f"提交评估结果失败: {str(e)}"), 0)
        finally:
            # 关闭加载对话框
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)

    def open_questionnaire_form(self, questionnaire, *args):
        """打开问卷填写表单"""
        try:
            # 根据mobile_distribution_format.md规范处理问卷数据
            logger.info(f"问卷详情字段: {list(questionnaire.keys())}")

            # 获取问卷名称
            questionnaire_name = questionnaire.get('name') or questionnaire.get('title') or '未命名问卷'
            questionnaire_description = questionnaire.get('description') or '无描述'

            # 获取问题列表
            questions = questionnaire.get('questions', [])

            # 如果没有questions字段，尝试从questionnaire_info获取
            if not questions and 'questionnaire_info' in questionnaire:
                questions = questionnaire['questionnaire_info'].get('questions', [])

            if not questions:
                logger.error("问卷没有题目数据")
                logger.error(f"questionnaire结构: {list(questionnaire.keys())}")
                self.show_error("该问卷没有题目数据")
                return

            logger.info(f"问卷题目数量: {len(questions)}")
            if questions:
                logger.info(f"第一个题目的字段: {list(questions[0].keys()) if isinstance(questions[0], dict) else '非字典类型'}")

            # 创建滚动视图
            from kivy.uix.scrollview import ScrollView
            scroll_view = ScrollView(size_hint=(1, None), size=(400, 500))

            # 创建表单布局
            from kivy.uix.boxlayout import BoxLayout
            form_layout = BoxLayout(orientation='vertical', spacing=15, padding=20, size_hint_y=None)
            form_layout.bind(minimum_height=form_layout.setter('height'))

            # 添加问卷标题
            from kivy.uix.label import Label
            title_label = Label(
                text=questionnaire_name,
                font_size='18sp',
                halign='center',
                valign='middle',
                size_hint_y=None,
                height=50,
                text_size=(380, 50),
                color=(0.2, 0.2, 0.2, 1)  # 深灰色
            )
            form_layout.add_widget(title_label)

            # 添加问卷说明
            if questionnaire_description and questionnaire_description != '无描述':
                desc_label = Label(
                    text=questionnaire_description,
                    font_size='14sp',
                    size_hint_y=None,
                    height=60,
                    halign='center',
                    valign='middle',
                    text_size=(380, 60),
                    color=(0.4, 0.4, 0.4, 1)  # 中灰色
                )
                form_layout.add_widget(desc_label)

            # 创建答案字段列表
            answer_fields = []

            # 添加题目和选项
            for i, q in enumerate(questions):
                # 调试：打印每个题目的字段
                logger.info(f"问卷题目 {i+1} 字段: {list(q.keys()) if isinstance(q, dict) else '非字典类型'}")

                # 尝试从不同字段获取题目文本
                question_text = ''
                possible_text_fields = ['text', 'question_text', 'title', 'content', 'description']
                for field in possible_text_fields:
                    if field in q and q[field]:
                        question_text = q[field]
                        break

                if not question_text:
                    logger.warning(f"问卷题目 {i+1} 没有文本内容，跳过")
                    continue

                question_label = Label(
                    text=f"{i+1}. {question_text}",
                    size_hint_y=None,
                    height=40,
                    halign='left',
                    valign='middle',
                    text_size=(400, 40)
                )
                form_layout.add_widget(question_label)

                # 检查题目类型
                question_type = q.get('type') or q.get('question_type', 'text')

                # 尝试从不同字段获取选项
                options = None
                possible_option_fields = ['options', 'choices', 'answers']
                for field in possible_option_fields:
                    if field in q and q[field]:
                        options = q[field]
                        logger.info(f"问卷题目 {i+1} 找到选项在字段: {field}")
                        break

                if options and question_type in ['single_choice', 'multiple_choice', 'radio', 'checkbox']:
                    # 选择题：显示选项
                    from kivy.uix.checkbox import CheckBox
                    from kivy.uix.label import Label
                    from kivy.uix.boxlayout import BoxLayout

                    option_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=len(options) * 40)
                    radio_group = []

                    for option in options:
                        if isinstance(option, dict):
                            option_text = option.get('text', '') or option.get('label', '') or option.get('value', '')
                            option_value = option.get('value', '') or option_text
                        else:
                            option_text = str(option)
                            option_value = option_text

                        # 创建复选框（用作单选按钮）
                        checkbox = CheckBox(
                            group=f"question_{q.get('id', i)}" if question_type == 'single_choice' else None,
                            size_hint=(None, None),
                            size=(48, 48),
                            active=False
                        )
                        checkbox.value = option_value
                        radio_group.append(checkbox)

                        # 创建标签
                        label = Label(
                            text=option_text,
                            size_hint_x=1,
                            halign='left',
                            valign='middle',
                            text_size=(None, 40)
                        )

                        # 添加到布局
                        option_row = BoxLayout(orientation='horizontal', size_hint_y=None, height=40)
                        option_row.add_widget(checkbox)
                        option_row.add_widget(label)
                        option_layout.add_widget(option_row)

                    form_layout.add_widget(option_layout)
                    answer_fields.append((q, radio_group))
                else:
                    # 文本题：显示输入框
                    from kivymd.uix.textfield import MDTextField
                    ti = MDBoxLayout()
                    answer = MDTextField(hint_text='请输入答案', multiline=False)
                    ti.add_widget(answer)
                    form_layout.add_widget(ti)
                    answer_fields.append((q, answer))

            # 设置表单布局高度
            form_layout.height = sum(c.height for c in form_layout.children)

            # 添加表单到滚动视图
            scroll_view.add_widget(form_layout)

            # 创建对话框
            def on_submit(instance):
                # 收集答案
                answers = []
                for q, answer_widget in answer_fields:
                    if isinstance(answer_widget, list):
                        # 选择题：从radio_group中获取选中的选项
                        selected_option = None
                        for checkbox in answer_widget:
                            if checkbox.active:
                                selected_option = checkbox.value
                                break

                        answers.append({
                            'question_id': q.get('id', ''),
                            'question_text': q.get('text', ''),
                            'answer': selected_option
                        })
                    else:
                        # 文本题：从输入框获取文本
                        answers.append({
                            'question_id': q.get('id', ''),
                            'question_text': q.get('text', ''),
                            'answer': answer_widget.text
                        })

                response_data = {
                    'questionnaire_id': questionnaire.get('id'),
                    'questionnaire_title': questionnaire.get('name') or questionnaire.get('title'),
                    'answers': answers
                }
                self.submit_response(response_data)
                dialog.dismiss()

            from kivy.uix.popup import Popup
            from kivy.uix.button import Button

            # 创建按钮布局
            button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)
            submit_button = Button(text='提交')
            cancel_button = Button(text='取消')
            button_layout.add_widget(submit_button)
            button_layout.add_widget(cancel_button)

            # 创建内容布局
            content_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
            content_layout.add_widget(scroll_view)
            content_layout.add_widget(button_layout)

            # 创建弹窗
            dialog = Popup(
                title=questionnaire.get('name') or questionnaire.get('title', '问卷填写'),
                content=content_layout,
                size_hint=(0.9, 0.9),
                auto_dismiss=False
            )

            # 绑定按钮事件
            submit_button.bind(on_release=on_submit)
            cancel_button.bind(on_release=dialog.dismiss)
            dialog.open()
        except Exception as e:
            logger.error(f"显示问卷表单时出错: {str(e)}")
            self.show_error(f"无法显示问卷表单: {str(e)}")

    def submit_response(self, response_data):
        """提交问卷答卷"""
        manager = get_survey_manager()
        result = manager.submit_response(response_data, user_id=getattr(self.current_user, 'user_id', None))
        if result:
            self.show_info("提交成功！")
            self.load_response_history()
        else:
            self.show_info("提交失败，请重试。")

    def show_response_detail(self, response, *args):
        """展示历史答卷详情"""
        content = ''
        for ans in response.get('answers', []):
            content += f"[b]{ans.get('question', '')}[/b]\n{ans.get('answer', '')}\n\n"
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer

        dialog = MDDialog(
            MDDialogHeadlineText(
                text=response.get('questionnaire_title', '答卷详情'),
                halign="center"
            ),
            MDDialogSupportingText(
                text=content,
                halign="left",
                markup=True
            ),
            MDDialogButtonContainer(
                MDButton(
                    text='关闭',
                    style='text',
                    on_release=lambda x: dialog.dismiss()
                ),
                spacing="8dp",
                pos_hint={"center_x": 0.5}
            )
        )
        dialog.open()

    def show_info(self, msg):
        """显示信息提示"""
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

        MDSnackbar(
            MDSnackbarText(
                text=msg,
                theme_text_color="Custom",
                text_color=App.get_running_app().theme.TEXT_LIGHT
            ),
            y=dp(10),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9,
            md_bg_color=App.get_running_app().theme.INFO_COLOR,
            radius=[10, 10, 10, 10]
        ).open()

    def show_error(self, msg):
        """显示错误提示"""
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

        MDSnackbar(
            MDSnackbarText(
                text=msg,
                theme_text_color="Custom",
                text_color=App.get_running_app().theme.TEXT_LIGHT
            ),
            y=dp(10),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9,
            md_bg_color=App.get_running_app().theme.ERROR_COLOR,
            radius=[10, 10, 10, 10]
        ).open()

    def show_login_required(self):
        """显示登录提示对话框，并尝试重新获取用户信息"""
        # 使用认证管理器尝试重新加载用户信息
        try:
            # 尝试重新加载用户认证信息
            if self.auth_manager.reload_user_auth():
                logger.info("成功重新加载用户认证信息")

                # 重新获取当前用户
                user_manager = get_user_manager()
                self.current_user = user_manager.get_current_user()

                # 重新配置API客户端
                if self.api_client:
                    self.auth_manager.setup_api_client(self.api_client)

                # 重新加载数据
                self.load_assessments()
                self.load_questionnaires()
                self.load_response_history()
                return

        except Exception as e:
            logger.error(f"尝试重新加载用户时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        # 如果无法重新加载用户，显示登录提示
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer

        dialog = MDDialog(
            MDDialogHeadlineText(
                text="请先登录",
                halign="center"
            ),
            MDDialogSupportingText(
                text="请登录后再填写问卷/量表。",
                halign="center"
            ),
            MDDialogButtonContainer(
                MDButton(
                    text="确定",
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                spacing="8dp",
                pos_hint={"center_x": 0.5}
            )
        )
        dialog.open()