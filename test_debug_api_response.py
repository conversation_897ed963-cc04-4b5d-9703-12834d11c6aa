#!/usr/bin/env python3
"""
测试API响应调试
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_api_response_debugging():
    """测试API响应调试功能"""
    print("=== 测试API响应调试功能 ===")
    
    # 模拟可能的云端API响应格式
    test_responses = [
        {
            "name": "空数据响应",
            "response": {
                "status": "success",
                "data": []
            },
            "expected_count": 0
        },
        {
            "name": "直接数组响应",
            "response": {
                "status": "success",
                "data": [
                    {
                        "id": 123,
                        "name": "汉密尔顿抑郁量表",
                        "assessment_type": "depression",
                        "status": "pending",
                        "template": {
                            "id": 1,
                            "name": "汉密尔顿抑郁量表",
                            "questions": [
                                {
                                    "id": 1,
                                    "question_text": "您的心情如何？",
                                    "question_type": "single_choice",
                                    "options": ["很好", "一般", "较差", "很差"]
                                }
                            ]
                        }
                    }
                ]
            },
            "expected_count": 1
        },
        {
            "name": "分页格式响应",
            "response": {
                "status": "success",
                "data": {
                    "assessments": [
                        {
                            "id": 456,
                            "name": "焦虑自评量表",
                            "assessment_type": "anxiety",
                            "status": "pending"
                        }
                    ],
                    "total": 1,
                    "skip": 0,
                    "limit": 20
                }
            },
            "expected_count": 1
        },
        {
            "name": "无template字段响应",
            "response": {
                "status": "success",
                "data": [
                    {
                        "id": 789,
                        "name": "压力评估量表",
                        "assessment_type": "stress",
                        "status": "pending",
                        "questions": [
                            {
                                "id": 1,
                                "text": "您感到压力吗？",
                                "type": "single_choice"
                            }
                        ]
                    }
                ]
            },
            "expected_count": 1
        },
        {
            "name": "无效数据响应",
            "response": {
                "status": "success",
                "data": [
                    {
                        "invalid": "data",
                        "no_id": True
                    },
                    "string_item",
                    None,
                    {
                        "id": 999,
                        "name": "有效量表",
                        "status": "pending"
                    }
                ]
            },
            "expected_count": 1  # 只有最后一个是有效的
        }
    ]
    
    def process_assessment_response(result):
        """模拟修复后的评估量表响应处理逻辑"""
        print(f"  响应类型: {type(result)}")
        print(f"  响应键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if not result or not (result.get('status') == 'success' or result.get('success')):
            print("  响应状态不成功")
            return []
        
        data = result.get('data', [])
        print(f"  data字段类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"  data是字典，键: {list(data.keys())}")
        elif isinstance(data, list):
            print(f"  data是列表，长度: {len(data)}")
            if data:
                print(f"  第一个元素类型: {type(data[0])}")
                if isinstance(data[0], dict):
                    print(f"  第一个元素键: {list(data[0].keys())}")
        
        # 处理不同的响应格式
        assessments = []
        if isinstance(data, list):
            # 直接是评估量表列表
            assessments = data
        elif isinstance(data, dict):
            # 可能是分页格式或包装格式
            assessments = data.get('assessments', data.get('items', []))
        
        print(f"  提取到的评估量表数量: {len(assessments)}")
        
        # 过滤并验证评估量表数据
        filtered_assessments = []
        for assessment in assessments:
            if isinstance(assessment, dict):
                try:
                    # 支持多种ID字段名，按优先级处理
                    assessment_id = (assessment.get('id') or
                                   assessment.get('assessment_id') or
                                   assessment.get('distribution_id') or
                                   assessment.get('template_id'))
                    
                    # 获取评估量表名称
                    assessment_name = (assessment.get('name') or
                                     assessment.get('title') or
                                     assessment.get('assessment_name'))
                    
                    print(f"    检查评估量表: ID={assessment_id}, Name={assessment_name}")
                    
                    if assessment_id and assessment_name:
                        # 确保template字段存在（按照文档规范）
                        if 'template' not in assessment and 'questions' in assessment:
                            # 如果没有template字段但有questions，创建template结构
                            assessment['template'] = {
                                'id': assessment.get('template_id', assessment_id),
                                'name': assessment_name,
                                'assessment_type': assessment.get('assessment_type', 'general'),
                                'version': assessment.get('version', '1.0'),
                                'description': assessment.get('description', ''),
                                'instructions': assessment.get('instructions', ''),
                                'questions': assessment.get('questions', [])
                            }
                            print(f"    创建template字段")
                        
                        filtered_assessments.append(assessment)
                        print(f"    添加评估量表: {assessment_name} (ID: {assessment_id})")
                    else:
                        print(f"    跳过无效的评估量表: ID={assessment_id}, Name={assessment_name}")
                except Exception as item_error:
                    print(f"    处理评估量表项时出错: {str(item_error)}")
                    continue
            else:
                print(f"    跳过非字典类型的评估量表项: {type(assessment)}")
        
        print(f"  最终有效评估量表数量: {len(filtered_assessments)}")
        return filtered_assessments
    
    passed = 0
    total = len(test_responses)
    
    for test_case in test_responses:
        print(f"\n测试: {test_case['name']}")
        response = test_case["response"]
        expected_count = test_case["expected_count"]
        
        try:
            processed = process_assessment_response(response)
            actual_count = len(processed)
            
            if actual_count == expected_count:
                print(f"✓ 测试通过: 期望={expected_count}, 实际={actual_count}")
                passed += 1
            else:
                print(f"✗ 测试失败: 期望={expected_count}, 实际={actual_count}")
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def test_user_id_deprecation():
    """测试user_id弃用处理"""
    print("\n=== 测试user_id弃用处理 ===")
    
    # 模拟包含user_id的旧格式数据
    old_format_data = {
        "user_id": 123,
        "custom_id": "SM_006",
        "name": "张三",
        "role": "patient"
    }
    
    def process_user_data(user_data):
        """处理用户数据，忽略user_id"""
        processed = {}
        
        # 只保留custom_id和其他有用字段
        if 'custom_id' in user_data:
            processed['custom_id'] = user_data['custom_id']
        
        if 'name' in user_data:
            processed['name'] = user_data['name']
        
        if 'role' in user_data:
            processed['role'] = user_data['role']
        
        # 忽略user_id字段
        if 'user_id' in user_data:
            print(f"忽略已弃用的user_id字段: {user_data['user_id']}")
        
        return processed
    
    processed = process_user_data(old_format_data)
    
    # 验证结果
    expected_keys = {'custom_id', 'name', 'role'}
    actual_keys = set(processed.keys())
    
    if actual_keys == expected_keys and 'user_id' not in processed:
        print("✓ user_id弃用处理正确")
        return True
    else:
        print(f"✗ user_id弃用处理失败: 期望键={expected_keys}, 实际键={actual_keys}")
        return False

def main():
    """主测试函数"""
    print("开始测试API响应调试和user_id弃用处理...\n")
    
    test1_passed = test_api_response_debugging()
    test2_passed = test_user_id_deprecation()
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！")
        print("\n修复说明:")
        print("1. ✅ 增强API响应调试 - 详细记录响应结构和处理过程")
        print("2. ✅ user_id弃用处理 - 完全使用custom_id，忽略user_id")
        print("3. ✅ 多种响应格式支持 - 直接数组、分页格式、包装格式")
        print("4. ✅ 数据验证增强 - 更严格的ID和名称验证")
        print("5. ✅ template字段自动创建 - 兼容无template字段的响应")
        print("\n现在请重新测试移动应用，查看详细的调试日志:")
        print("- 检查API响应的实际结构")
        print("- 确认数据处理的每个步骤")
        print("- 验证最终的有效量表/问卷数量")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
