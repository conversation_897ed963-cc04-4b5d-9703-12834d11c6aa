#!/usr/bin/env python3
"""
直接测试云端API端点
"""

import requests
import json
import os

def test_cloud_endpoints():
    """直接测试云端API端点"""
    print("=== 直接测试云端API端点 ===")

    # 配置
    base_url = "http://8.138.188.26:80/api"
    custom_id = "SM_006"

    # 从认证文件读取token
    auth_files = [
        "data/cloud_auth.json",
        "data/auth_info.json",
        "auth_info.json"
    ]
    token = None

    for auth_file in auth_files:
        try:
            if os.path.exists(auth_file):
                with open(auth_file, 'r', encoding='utf-8') as f:
                    auth_data = json.load(f)
                    token = auth_data.get('token') or auth_data.get('access_token')
                    if token:
                        print(f"从认证文件 {auth_file} 读取到token: {token[:20]}...")
                        break
        except Exception as e:
            print(f"读取认证文件 {auth_file} 失败: {e}")
            continue

    if not token:
        print("没有找到有效的认证文件或token")
        return False

    if not token:
        print("没有找到有效的token")
        return False

    # 准备请求头
    headers = {
        'Authorization': f'Bearer {token}',
        'X-User-ID': custom_id,
        'Content-Type': 'application/json'
    }

    print(f"请求头: {headers}")

    # 测试端点列表
    endpoints = [
        {
            "name": "移动端评估量表",
            "url": f"{base_url}/mobile/assessments",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "移动端问卷",
            "url": f"{base_url}/mobile/questionnaires",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "标准评估量表",
            "url": f"{base_url}/assessments",
            "params": {"custom_id": custom_id}
        },
        {
            "name": "标准问卷",
            "url": f"{base_url}/questionnaires",
            "params": {"custom_id": custom_id}
        }
    ]

    results = []

    for endpoint in endpoints:
        print(f"\n--- 测试 {endpoint['name']} ---")
        print(f"URL: {endpoint['url']}")
        print(f"参数: {endpoint['params']}")

        try:
            response = requests.get(
                endpoint['url'],
                headers=headers,
                params=endpoint['params'],
                timeout=10
            )

            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应类型: {type(data)}")

                    if isinstance(data, dict):
                        print(f"响应键: {list(data.keys())}")

                        if 'status' in data:
                            print(f"状态: {data['status']}")

                        if 'data' in data:
                            data_content = data['data']
                            print(f"data类型: {type(data_content)}")

                            if isinstance(data_content, list):
                                print(f"data长度: {len(data_content)}")
                                if data_content:
                                    print(f"第一个元素: {data_content[0]}")
                            elif isinstance(data_content, dict):
                                print(f"data字典键: {list(data_content.keys())}")
                            else:
                                print(f"data内容: {data_content}")

                        # 只显示前200个字符的响应内容
                        response_str = json.dumps(data, indent=2, ensure_ascii=False)
                        if len(response_str) > 200:
                            print(f"响应内容（前200字符）: {response_str[:200]}...")
                        else:
                            print(f"完整响应: {response_str}")
                    else:
                        print(f"响应内容: {data}")

                    results.append({
                        "endpoint": endpoint['name'],
                        "status": "success",
                        "data": data
                    })

                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    print(f"原始响应: {response.text[:200]}...")
                    results.append({
                        "endpoint": endpoint['name'],
                        "status": "json_error",
                        "error": str(e)
                    })
            else:
                print(f"请求失败: {response.status_code}")
                print(f"错误响应: {response.text[:200]}...")
                results.append({
                    "endpoint": endpoint['name'],
                    "status": "http_error",
                    "status_code": response.status_code
                })

        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            results.append({
                "endpoint": endpoint['name'],
                "status": "request_error",
                "error": str(e)
            })

    # 总结结果
    print("\n=== 测试结果总结 ===")
    has_data = False

    for result in results:
        print(f"{result['endpoint']}: {result['status']}")
        if result['status'] == 'success':
            data = result['data']
            if isinstance(data, dict) and 'data' in data:
                data_content = data['data']
                if isinstance(data_content, list):
                    count = len(data_content)
                    print(f"  -> 返回 {count} 个项目")
                    if count > 0:
                        has_data = True
                elif isinstance(data_content, dict):
                    print(f"  -> 返回字典数据")
                    if data_content:
                        has_data = True

    if has_data:
        print(f"\n🎉 找到有数据的端点！")
    else:
        print(f"\n❌ 所有端点都没有返回数据")
        print("\n可能的原因:")
        print("1. 云端没有为用户 SM_006 分发量表/问卷")
        print("2. 分发记录的状态不是 'pending'")
        print("3. API实现有问题")
        print("\n建议检查:")
        print("SELECT * FROM assessment_distributions WHERE user_custom_id = 'SM_006';")
        print("SELECT * FROM questionnaire_distributions WHERE user_custom_id = 'SM_006';")

    return has_data

def main():
    """主函数"""
    print("开始直接测试云端API端点...\n")

    success = test_cloud_endpoints()

    if success:
        print("\n✅ 找到了有数据的API端点")
    else:
        print("\n❌ 没有找到有数据的API端点")

    return success

if __name__ == "__main__":
    success = main()
    print(f"\n测试完成，结果: {'成功' if success else '失败'}")
