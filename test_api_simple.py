#!/usr/bin/env python3
"""
简单测试API调用
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_mobile_api():
    """测试移动端API调用"""
    print("=== 测试移动端API调用 ===")
    
    try:
        # 导入云端API客户端
        from utils.cloud_api import CloudAPI
        
        # 创建API客户端
        api = CloudAPI()
        
        # 设置用户信息
        custom_id = "SM_006"
        
        print(f"测试用户: {custom_id}")
        
        # 测试评估量表API
        print("\n--- 测试评估量表API ---")
        try:
            result = api.get_mobile_assessments(custom_id)
            print(f"评估量表API结果类型: {type(result)}")
            print(f"评估量表API结果: {result}")
            
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    data = result.get('data', [])
                    print(f"评估量表数据类型: {type(data)}")
                    print(f"评估量表数据长度: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
                    print(f"评估量表数据内容: {data}")
                else:
                    print(f"评估量表API失败: {result.get('message', '未知错误')}")
            else:
                print(f"评估量表API返回异常格式: {result}")
                
        except Exception as e:
            print(f"评估量表API调用异常: {e}")
        
        # 测试问卷API
        print("\n--- 测试问卷API ---")
        try:
            result = api.get_mobile_questionnaires(custom_id)
            print(f"问卷API结果类型: {type(result)}")
            print(f"问卷API结果: {result}")
            
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    data = result.get('data', [])
                    print(f"问卷数据类型: {type(data)}")
                    print(f"问卷数据长度: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
                    print(f"问卷数据内容: {data}")
                else:
                    print(f"问卷API失败: {result.get('message', '未知错误')}")
            else:
                print(f"问卷API返回异常格式: {result}")
                
        except Exception as e:
            print(f"问卷API调用异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试过程中出现异常: {e}")
        return False

def test_alternative_endpoints():
    """测试其他可能的端点"""
    print("\n=== 测试其他可能的端点 ===")
    
    try:
        from utils.cloud_api import CloudAPI
        
        api = CloudAPI()
        custom_id = "SM_006"
        
        # 测试标准评估量表端点
        print("\n--- 测试标准评估量表端点 ---")
        try:
            # 直接调用_make_request方法测试不同端点
            headers = {
                'Authorization': f'Bearer {api.auth_info.get("access_token", "")}',
                'X-User-ID': custom_id,
                'Content-Type': 'application/json'
            }
            
            # 测试不同的端点
            endpoints_to_test = [
                "assessments",
                "questionnaires", 
                "user-assessments",
                "distributed-assessments",
                "user-questionnaires",
                "distributed-questionnaires"
            ]
            
            for endpoint in endpoints_to_test:
                print(f"\n测试端点: {endpoint}")
                try:
                    result = api._make_request(
                        method="GET",
                        endpoint=endpoint,
                        headers=headers,
                        max_retries=1
                    )
                    
                    print(f"  结果类型: {type(result)}")
                    if isinstance(result, dict):
                        print(f"  结果键: {list(result.keys())}")
                        if result.get('status') == 'success':
                            data = result.get('data', [])
                            print(f"  数据类型: {type(data)}")
                            if isinstance(data, list):
                                print(f"  数据长度: {len(data)}")
                                if data:
                                    print(f"  第一个元素: {data[0]}")
                            elif isinstance(data, dict):
                                print(f"  数据键: {list(data.keys())}")
                                for key in ['assessments', 'questionnaires', 'items']:
                                    if key in data:
                                        items = data[key]
                                        print(f"  {key}长度: {len(items) if isinstance(items, list) else 'N/A'}")
                                        if isinstance(items, list) and items:
                                            print(f"  第一个{key}: {items[0]}")
                        else:
                            print(f"  失败: {result.get('message', '未知错误')}")
                    else:
                        print(f"  异常结果: {result}")
                        
                except Exception as e:
                    print(f"  端点 {endpoint} 测试异常: {e}")
            
        except Exception as e:
            print(f"测试其他端点时出现异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试其他端点过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    print("开始简单API测试...\n")
    
    test1_passed = test_mobile_api()
    test2_passed = test_alternative_endpoints()
    
    if test1_passed and test2_passed:
        print("\n✅ API测试完成")
        print("\n请检查上面的输出，寻找:")
        print("1. 哪个端点返回了实际数据")
        print("2. 数据的具体格式和结构")
        print("3. 是否有错误信息")
        return True
    else:
        print("\n❌ API测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
